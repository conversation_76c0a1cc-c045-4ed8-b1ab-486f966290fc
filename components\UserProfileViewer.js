// Componente para visualizar perfil de outros usuários
import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { useAuth } from '../contexts/AuthContext';
import { useFriends } from '../contexts/FriendsContext';
import styles from '../styles/UserProfileViewer.module.css';
import { getBadge } from '../data/badges';
import { getAchievement } from '../data/achievements';

const UserProfileViewer = ({ isOpen, userId, username, onClose }) => {
  const { isAuthenticated } = useAuth();
  const { sendFriendRequest, friends } = useFriends();
  
  const [profile, setProfile] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);

  // Carregar perfil do usuário
  useEffect(() => {
    if (isOpen && (userId || username)) {
      loadUserProfile();
    }
  }, [isOpen, userId, username]);

  // Debug logs quando profile muda
  useEffect(() => {
    if (profile) {
      console.log('🎯 Profile carregado no useEffect:', profile);
      console.log('🎯 Profile.stats:', profile.stats);
      console.log('🎯 Profile.statistics:', profile.statistics);
      console.log('🎯 Todas as chaves do profile:', Object.keys(profile));
    }
  }, [profile]);

  const loadUserProfile = async () => {
    console.log('🔥 UserProfileViewer: Carregando perfil para', { userId, username });
    setIsLoading(true);
    setError(null);

    try {
      const sessionToken = localStorage.getItem('ludomusic_session_token');
      const params = new URLSearchParams();
      
      if (userId) params.append('userId', userId);
      if (username) params.append('username', username);

      const headers = {
        'Content-Type': 'application/json'
      };

      if (sessionToken) {
        headers['Authorization'] = `Bearer ${sessionToken}`;
      }

      const response = await fetch(`/api/user-profile?${params.toString()}`, {
        headers
      });

      if (response.ok) {
        const data = await response.json();
        console.log('🔍 Dados recebidos no UserProfileViewer:', data.profile);
        setProfile(data.profile);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Erro ao carregar perfil');
      }
    } catch (error) {
      console.error('Erro ao carregar perfil:', error);
      setError('Erro de conexão');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddFriend = async () => {
    if (!profile || !isAuthenticated) return;

    setActionLoading(true);
    try {
      await sendFriendRequest({
        id: profile.id,
        username: profile.username,
        displayName: profile.displayName,
        avatar: profile.avatar
      });

      // Recarregar perfil para atualizar status de relacionamento
      await loadUserProfile();
      
      alert('Solicitação de amizade enviada!');
    } catch (error) {
      alert(error.message || 'Erro ao enviar solicitação');
    } finally {
      setActionLoading(false);
    }
  };

  const getRelationshipButton = () => {
    if (!isAuthenticated || !profile?.relationship) return null;

    const { isFriend, hasPendingRequest, requestType, isOwnProfile } = profile.relationship;

    if (isOwnProfile) {
      return (
        <button className={styles.ownProfileButton} disabled>
          👤 Seu Perfil
        </button>
      );
    }

    if (isFriend) {
      return (
        <button className={styles.friendButton} disabled>
          ✅ Amigos
        </button>
      );
    }

    if (hasPendingRequest) {
      if (requestType === 'sent') {
        return (
          <button className={styles.pendingButton} disabled>
            ⏳ Solicitação Enviada
          </button>
        );
      } else {
        return (
          <button className={styles.receivedButton} disabled>
            📩 Solicitação Recebida
          </button>
        );
      }
    }

    return (
      <button 
        className={styles.addFriendButton}
        onClick={handleAddFriend}
        disabled={actionLoading}
      >
        {actionLoading ? '⏳ Enviando...' : '➕ Adicionar Amigo'}
      </button>
    );
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getXPProgress = () => {
    if (!profile) return 0;

    // Tentar diferentes campos onde o XP pode estar
    const xp = profile.xp || profile.stats?.xp || 0;
    const level = profile.level || profile.stats?.level || 1;

    console.log('🔥 XP Debug:', { xp, level, profile: profile });

    const currentLevelXP = (level - 1) * 1000;
    const nextLevelXP = level * 1000;
    const progress = ((xp - currentLevelXP) / (nextLevelXP - currentLevelXP)) * 100;
    const finalProgress = Math.max(0, Math.min(100, progress));

    console.log('📊 XP Calculation:', {
      xp,
      level,
      currentLevelXP,
      nextLevelXP,
      progress,
      finalProgress
    });

    return finalProgress;
  };

  // Bloquear scroll da página quando modal estiver aberto
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
      console.log('🔒 Bloqueando scroll da página');
    } else {
      document.body.style.overflow = 'unset';
      console.log('🔓 Liberando scroll da página');
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Se não estiver aberto, não renderizar nada
  if (!isOpen) return null;

  if (isLoading) {
    const loadingContent = (
      <div className={styles.overlay}>
        <div className={styles.modal}>
          <div className={styles.loading}>
            <div className={styles.spinner}></div>
            <p>Carregando perfil...</p>
          </div>
        </div>
      </div>
    );
    return typeof document !== 'undefined'
      ? createPortal(loadingContent, document.body)
      : null;
  }

  if (error) {
    const errorContent = (
      <div className={styles.overlay}>
        <div className={styles.modal}>
          <div className={styles.error}>
            <h3>❌ Erro</h3>
            <p>{error}</p>
            <button onClick={onClose} className={styles.closeButton}>
              Fechar
            </button>
          </div>
        </div>
      </div>
    );
    return typeof document !== 'undefined'
      ? createPortal(errorContent, document.body)
      : null;
  }

  if (!profile) {
    return null;
  }

  // Debug: verificar dados do perfil
  console.log('🔍 DADOS COMPLETOS DO PERFIL:', profile);
  console.log('🏆 ACHIEVEMENTS NO PERFIL:', profile.achievements);
  console.log('🎖️ BADGES NO PERFIL:', profile.badges);
  console.log('📊 STATS NO PERFIL:', profile.stats);
  console.log('🔑 TODAS AS CHAVES:', Object.keys(profile));

  const modalContent = (
    <div className={styles.overlay}>
      <div className={styles.modal}>
        <div className={styles.header}>
          <button onClick={onClose} className={styles.closeButton}>
            ✕
          </button>
          <h2>Perfil do Jogador</h2>
        </div>

        <div className={styles.content}>
          {/* Informações básicas */}
          <div className={styles.basicInfo}>
            <div className={styles.avatar}>
              {/* Renderizar avatar de forma segura */}
              {profile.avatar && typeof profile.avatar === 'string' ? (
                profile.avatar.startsWith('http') || profile.avatar.startsWith('/') || profile.avatar.startsWith('data:') ? (
                  <img
                    src={profile.avatar}
                    alt="Avatar"
                    className={styles.avatarImage}
                    onLoad={() => console.log('✅ Avatar carregou com sucesso')}
                    onError={(e) => console.log('❌ Erro ao carregar avatar:', e)}
                  />
                ) : (
                  profile.avatar
                )
              ) : '👤'}
            </div>
            <div className={styles.userInfo}>
              <h3 className={styles.displayName}>{profile.displayName}</h3>
              <p className={styles.username}>@{profile.username}</p>
              {profile.bio && (
                <p className={styles.bio}>{profile.bio}</p>
              )}
              <div className={styles.level}>
                <span className={styles.levelBadge}>
                  Nível {profile.level || profile.stats?.level || 1}
                </span>
                <div className={styles.xpBar}>
                  <div
                    className={styles.xpProgress}
                    style={{ width: `${getXPProgress()}%` }}
                  ></div>
                </div>
                <span className={styles.xpText}>
                  {profile.xp || profile.stats?.xp || 0} XP
                </span>
              </div>
            </div>
          </div>

          {/* Botão de relacionamento */}
          <div className={styles.actions}>
            {getRelationshipButton()}
          </div>

          {/* Estatísticas */}
          <div className={styles.statistics}>
            <h4>📊 Estatísticas de Jogo</h4>
            <div className={styles.statsGrid}>
              <div className={styles.statItem}>
                <span className={styles.statValue}>
                  {profile.statistics?.totalGames || profile.totalGames || 0}
                </span>
                <span className={styles.statLabel}>Partidas Jogadas</span>
              </div>
              <div className={styles.statItem}>
                <span className={styles.statValue}>
                  {profile.statistics?.gamesWon || profile.gamesWon || 0}
                </span>
                <span className={styles.statLabel}>Vitórias</span>
              </div>
              <div className={styles.statItem}>
                <span className={styles.statValue}>
                  {Math.round(profile.statistics?.winRate || 0)}%
                </span>
                <span className={styles.statLabel}>Taxa de Vitória</span>
              </div>
              <div className={styles.statItem}>
                <span className={styles.statValue}>
                  {profile.statistics?.bestStreak || profile.bestStreak || 0}
                </span>
                <span className={styles.statLabel}>Melhor Sequência</span>
              </div>
              <div className={styles.statItem}>
                <span className={styles.statValue}>
                  {profile.statistics?.currentStreak || profile.currentStreak || 0}
                </span>
                <span className={styles.statLabel}>Sequência Atual</span>
              </div>
              <div className={styles.statItem}>
                <span className={styles.statValue}>
                  {profile.statistics?.perfectGames || 0}
                </span>
                <span className={styles.statLabel}>Jogos Perfeitos</span>
              </div>
            </div>
          </div>

          {/* Conquistas */}
          <div className={styles.achievements}>
            <h4>🏆 Conquistas</h4>
            {(() => {
              // Tentar diferentes campos onde as conquistas podem estar
              const achievements = profile.achievements || profile.unlockedAchievements || [];
              console.log('🏆 Conquistas encontradas:', achievements);

              if (achievements && achievements.length > 0) {
                return (
                  <>
                    <div className={styles.achievementsList}>
                      {achievements.slice(0, 6).map((achievementId, index) => {
                        const achievementData = getAchievement(achievementId);
                        console.log('🔍 Achievement data para', achievementId, ':', achievementData);
                        if (!achievementData) return null;
                        return (
                          <div key={index} className={styles.achievementItem} title={achievementData.description}>
                            <span className={styles.achievementIcon}>{achievementData.icon}</span>
                            <div className={styles.achievementInfo}>
                              <span className={styles.achievementName}>{achievementData.title}</span>
                              <span className={styles.achievementDate}>
                                Desbloqueada
                              </span>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                    {achievements.length > 6 && (
                      <p className={styles.moreAchievements}>
                        +{achievements.length - 6} conquistas
                      </p>
                    )}
                  </>
                );
              } else {
                return (
                  <p className={styles.noAchievements}>
                    Nenhuma conquista desbloqueada ainda
                  </p>
                );
              }
            })()}
          </div>

          {/* Badges */}
          <div className={styles.badges}>
            <h4>🎖️ Badges</h4>
            {profile.badges && profile.badges.length > 0 ? (
              <div className={styles.badgesList}>
                {profile.badges.map((badgeId, index) => {
                  const badgeData = getBadge(badgeId);
                  if (!badgeData) return null;
                  return (
                    <div key={index} className={styles.badgeItem} title={badgeData.description}>
                      <span className={styles.badgeIcon}>{badgeData.icon}</span>
                      <span className={styles.badgeName}>{badgeData.title}</span>
                    </div>
                  );
                })}
              </div>
            ) : (
              <p className={styles.noBadges}>
                Nenhuma badge conquistada ainda
              </p>
            )}
          </div>

          {/* Informações adicionais */}
          <div className={styles.additionalInfo}>
            <p className={styles.joinDate}>
              📅 Membro desde {formatDate(profile.createdAt)}
            </p>
            <p className={styles.lastActive}>
              🕒 Última atividade: {formatDate(profile.lastActiveAt)}
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  // Usar portal para renderizar no body
  if (typeof document !== 'undefined') {
    console.log('🎯 Renderizando modal via portal no body');
    return createPortal(modalContent, document.body);
  }
  return null;
};

export default UserProfileViewer;
