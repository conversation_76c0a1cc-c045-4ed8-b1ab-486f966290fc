/* Estilos para o visualizador de perfil de usuário */

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999999;
  padding: 20px;
  backdrop-filter: blur(5px);
  overflow-y: auto;
}

.modal {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 20px;
  width: 100%;
  max-width: 500px;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  margin: auto;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header h2 {
  color: #ffffff;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.closeButton {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: #ffffff;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.content {
  padding: 30px;
}

/* Informações básicas */
.basicInfo {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 25px;
}

.avatar {
  font-size: 60px;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.avatarImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.userInfo {
  flex: 1;
}

.displayName {
  color: #ffffff;
  margin: 0 0 5px 0;
  font-size: 24px;
  font-weight: 700;
}

.username {
  color: #888888;
  margin: 0 0 10px 0;
  font-size: 14px;
}

.bio {
  color: #cccccc;
  margin: 0 0 15px 0;
  font-size: 13px;
  line-height: 1.4;
  font-style: italic;
  max-width: 300px;
}

.level {
  display: flex;
  align-items: center;
  gap: 10px;
}

.levelBadge {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.xpBar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.xpProgress {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #81C784);
  transition: width 0.3s ease;
}

.xpText {
  color: #888888;
  font-size: 12px;
  white-space: nowrap;
}

/* Ações */
.actions {
  margin-bottom: 25px;
  display: flex;
  justify-content: center;
}

.addFriendButton {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.addFriendButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
}

.addFriendButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.friendButton {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: default;
  font-size: 14px;
}

.pendingButton {
  background: linear-gradient(135deg, #FF9800, #F57C00);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: default;
  font-size: 14px;
}

.receivedButton {
  background: linear-gradient(135deg, #9C27B0, #7B1FA2);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: default;
  font-size: 14px;
}

.ownProfileButton {
  background: rgba(255, 255, 255, 0.1);
  color: #888888;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: default;
  font-size: 14px;
}

/* Estatísticas */
.statistics {
  margin-bottom: 25px;
}

.statistics h4 {
  color: #ffffff;
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
}

.statItem {
  background: rgba(255, 255, 255, 0.05);
  padding: 15px;
  border-radius: 12px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.statValue {
  display: block;
  color: #4CAF50;
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 5px;
}

.statLabel {
  color: #888888;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Conquistas */
.achievements {
  margin-bottom: 25px;
}

.achievements h4 {
  color: #ffffff;
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
}

.achievementsList {
  display: grid;
  gap: 10px;
}

.achievementItem {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.05);
  padding: 12px;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.achievementIcon {
  font-size: 24px;
  width: 32px;
  text-align: center;
}

.achievementInfo {
  flex: 1;
}

.achievementName {
  display: block;
  color: #ffffff;
  font-weight: 600;
  font-size: 14px;
}

.achievementDate {
  color: #888888;
  font-size: 12px;
}

.moreAchievements {
  color: #888888;
  text-align: center;
  margin: 10px 0 0 0;
  font-size: 12px;
}

/* Badges */
.badges {
  margin-bottom: 25px;
}

.badges h4 {
  color: #ffffff;
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
}

.badgesList {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.badgeItem {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.05);
  padding: 8px 12px;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.badgeIcon {
  font-size: 16px;
}

.badgeName {
  color: #ffffff;
  font-size: 12px;
  font-weight: 600;
}

/* Informações adicionais */
.additionalInfo {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 20px;
  text-align: center;
}

.joinDate,
.lastActive {
  color: #888888;
  margin: 5px 0;
  font-size: 12px;
}

/* Loading e Error */
.loading,
.error {
  text-align: center;
  padding: 40px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading p,
.error p {
  color: #ffffff;
  margin: 0;
}

.error h3 {
  color: #ff4444;
  margin: 0 0 15px 0;
}

/* Responsividade */
@media (max-width: 480px) {
  .overlay {
    padding: 10px;
  }

  .modal {
    max-height: 95vh;
  }

  .header,
  .content {
    padding: 20px;
  }

  .basicInfo {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .level {
    justify-content: center;
  }

  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .displayName {
    font-size: 20px;
  }
}
